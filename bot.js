const fetch = require('node-fetch');
const HttpsProxyAgent = require('https-proxy-agent');
const fs = require('fs');
const path = require('path');

// Fungsi untuk membaca proxy dari file proxy.txt
function loadProxiesFromFile() {
  try {
    const proxyFilePath = path.join(__dirname, 'proxy.txt');

    if (!fs.existsSync(proxyFilePath)) {
      console.log('❌ File proxy.txt tidak ditemukan!');
      return [];
    }

    const fileContent = fs.readFileSync(proxyFilePath, 'utf8');
    const lines = fileContent.split('\n').filter(line => line.trim() !== '');
    const proxies = [];

    for (const line of lines) {
      try {
        const proxyData = JSON.parse(line.trim());
        if (proxyData.proxy) {
          // Konversi format username:password@host:port ke *****************************:port
          const proxyUrl = proxyData.proxy.includes('://')
            ? proxyData.proxy
            : `http://${proxyData.proxy}`;
          proxies.push(proxyUrl);
        }
      } catch (parseError) {
        console.log(`⚠️ Gagal parsing line: ${line} -> ${parseError.message}`);
      }
    }

    console.log(`📋 Berhasil memuat ${proxies.length} proxy dari proxy.txt`);
    return proxies;
  } catch (error) {
    console.log(`❌ Error membaca file proxy.txt: ${error.message}`);
    return [];
  }
}

// Load proxy dari file
let proxies = loadProxiesFromFile();

// Fallback jika tidak ada proxy yang berhasil dimuat
if (proxies.length === 0) {
  console.log('⚠️ Tidak ada proxy yang valid, menggunakan proxy default...');
  proxies = [
    'http://123.456.789.001:8080',
    'http://111.222.333.444:3128',
  ];
}

async function checkProxy(proxy, index) {
  const agent = new HttpsProxyAgent(proxy);

  // Sembunyikan credentials dalam log untuk keamanan
  const safeProxy = proxy.replace(/\/\/[^@]+@/, '//***:***@');

  try {
    console.log(`🔄 Testing proxy ${index + 1}: ${safeProxy}`);

    const response = await fetch('https://httpbin.org/ip', {
      agent,
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.ok) throw new Error(`HTTP ${response.status}`);

    const data = await response.json();
    console.log(`✅ Proxy VALID: ${safeProxy} -> IP: ${data.origin}`);
    return { proxy, status: 'valid', ip: data.origin };

  } catch (error) {
    console.log(`❌ Proxy GAGAL: ${safeProxy} -> ${error.message}`);
    return { proxy, status: 'failed', error: error.message };
  }
}

(async () => {
  console.log(`🚀 Memulai pengecekan ${proxies.length} proxy...\n`);

  const results = [];

  for (let i = 0; i < proxies.length; i++) {
    const result = await checkProxy(proxies[i], i);
    results.push(result);

    // Delay kecil untuk menghindari rate limiting
    if (i < proxies.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // Tampilkan statistik
  const validProxies = results.filter(r => r.status === 'valid');
  const failedProxies = results.filter(r => r.status === 'failed');

  console.log('\n📊 STATISTIK HASIL:');
  console.log(`✅ Proxy Valid: ${validProxies.length}`);
  console.log(`❌ Proxy Gagal: ${failedProxies.length}`);
  console.log(`📈 Success Rate: ${((validProxies.length / results.length) * 100).toFixed(1)}%`);

  if (validProxies.length > 0) {
    console.log('\n🎯 PROXY YANG BERFUNGSI:');
    validProxies.forEach((proxy, index) => {
      const safeProxy = proxy.proxy.replace(/\/\/[^@]+@/, '//***:***@');
      console.log(`${index + 1}. ${safeProxy} -> ${proxy.ip}`);
    });
  }
})();
