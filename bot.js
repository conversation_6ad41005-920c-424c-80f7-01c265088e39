const fetch = require('node-fetch');
const HttpsProxyAgent = require('https-proxy-agent');

// Daftar proxy yang ingin diuji (format: http://IP:PORT)
const proxies = [
  'http://123.456.789.001:8080',
  'http://111.222.333.444:3128',
  // Tambahkan lebih banyak proxy di sini
];

async function checkProxy(proxy) {
  const agent = new HttpsProxyAgent(proxy);
  try {
    const response = await fetch('https://httpbin.org/ip', { agent, timeout: 8000 });
    if (!response.ok) throw new Error(`Status Code: ${response.status}`);
    const data = await response.json();
    console.log(`✅ Proxy valid: ${proxy} -> IP: ${data.origin}`);
  } catch (error) {
    console.log(`❌ Proxy gagal: ${proxy} -> ${error.message}`);
  }
}

(async () => {
  for (const proxy of proxies) {
    await checkProxy(proxy);
  }
})();
