const https = require('https');
const http = require('http');
const { HttpsProxyAgent } = require('https-proxy-agent');
const fs = require('fs');
const path = require('path');

// Fungsi untuk membaca proxy dari file proxy.txt
function loadProxiesFromFile() {
  try {
    const proxyFilePath = path.join(__dirname, 'proxy.txt');

    if (!fs.existsSync(proxyFilePath)) {
      console.log('❌ File proxy.txt tidak ditemukan!');
      return [];
    }

    const fileContent = fs.readFileSync(proxyFilePath, 'utf8');
    const lines = fileContent.split('\n').filter(line => line.trim() !== '');
    const proxies = [];

    for (const line of lines) {
      try {
        const proxyData = JSON.parse(line.trim());
        if (proxyData.proxy) {
          // Konversi format username:password@host:port ke *****************************:port
          const proxyUrl = proxyData.proxy.includes('://')
            ? proxyData.proxy
            : `http://${proxyData.proxy}`;
          proxies.push(proxyUrl);
        }
      } catch (parseError) {
        console.log(`⚠️ Gagal parsing line: ${line} -> ${parseError.message}`);
      }
    }

    console.log(`📋 Berhasil memuat ${proxies.length} proxy dari proxy.txt`);
    return proxies;
  } catch (error) {
    console.log(`❌ Error membaca file proxy.txt: ${error.message}`);
    return [];
  }
}

// Load proxy dari file
let proxies = loadProxiesFromFile();

// Fallback jika tidak ada proxy yang berhasil dimuat
if (proxies.length === 0) {
  console.log('⚠️ Tidak ada proxy yang valid, menggunakan proxy default...');
  proxies = [
    'http://123.456.789.001:8080',
    'http://111.222.333.444:3128',
  ];
}

async function testProxySpeed(proxy, agent) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const options = {
      hostname: 'httpbin.org',
      port: 443,
      path: '/bytes/1024', // Download 1KB untuk tes speed
      method: 'GET',
      agent: agent,
      timeout: 15000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    };

    const req = https.request(options, (res) => {
      let downloadedBytes = 0;

      res.on('data', (chunk) => {
        downloadedBytes += chunk.length;
      });

      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        const downloadSpeed = (downloadedBytes / 1024) / (responseTime / 1000); // KB/s

        if (res.statusCode !== 200) {
          reject(new Error(`HTTP ${res.statusCode}`));
          return;
        }

        resolve({
          responseTime,
          downloadSpeed: downloadSpeed.toFixed(2),
          downloadedBytes
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Speed test timeout'));
    });

    req.setTimeout(15000);
    req.end();
  });
}

async function checkProxy(proxy, index) {
  const agent = new HttpsProxyAgent(proxy);

  // Sembunyikan credentials dalam log untuk keamanan
  const safeProxy = proxy.replace(/\/\/[^@]+@/, '//***:***@');

  try {
    console.log(`🔄 Testing proxy ${index + 1}: ${safeProxy}`);

    // Test 1: Basic connectivity
    const startTime = Date.now();
    const data = await new Promise((resolve, reject) => {
      const options = {
        hostname: 'httpbin.org',
        port: 443,
        path: '/ip',
        method: 'GET',
        agent: agent,
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      };

      const req = https.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            if (res.statusCode !== 200) {
              reject(new Error(`HTTP ${res.statusCode}`));
              return;
            }
            const jsonData = JSON.parse(responseData);
            resolve(jsonData);
          } catch (parseError) {
            reject(new Error(`Parse error: ${parseError.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.setTimeout(10000);
      req.end();
    });

    const connectTime = Date.now() - startTime;

    // Test 2: Speed test
    console.log(`⚡ Testing speed for: ${safeProxy}`);
    const speedResult = await testProxySpeed(proxy, agent);

    console.log(`✅ Proxy VALID: ${safeProxy}`);
    console.log(`   📍 IP: ${data.origin}`);
    console.log(`   ⏱️  Response Time: ${connectTime}ms`);
    console.log(`   🚀 Download Speed: ${speedResult.downloadSpeed} KB/s`);
    console.log(`   📊 Speed Test Time: ${speedResult.responseTime}ms`);

    return {
      proxy,
      status: 'valid',
      ip: data.origin,
      connectTime,
      downloadSpeed: parseFloat(speedResult.downloadSpeed),
      speedTestTime: speedResult.responseTime
    };

  } catch (error) {
    console.log(`❌ Proxy GAGAL: ${safeProxy} -> ${error.message}`);
    return { proxy, status: 'failed', error: error.message };
  }
}

(async () => {
  console.log(`🚀 Memulai pengecekan ${proxies.length} proxy...\n`);

  const results = [];

  for (let i = 0; i < proxies.length; i++) {
    const result = await checkProxy(proxies[i], i);
    results.push(result);

    // Delay kecil untuk menghindari rate limiting
    if (i < proxies.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // Tampilkan statistik
  const validProxies = results.filter(r => r.status === 'valid');
  const failedProxies = results.filter(r => r.status === 'failed');

  console.log('\n📊 STATISTIK HASIL:');
  console.log(`✅ Proxy Valid: ${validProxies.length}`);
  console.log(`❌ Proxy Gagal: ${failedProxies.length}`);
  console.log(`📈 Success Rate: ${((validProxies.length / results.length) * 100).toFixed(1)}%`);

  if (validProxies.length > 0) {
    // Sort berdasarkan kecepatan download (tertinggi ke terendah)
    const sortedBySpeed = [...validProxies].sort((a, b) => b.downloadSpeed - a.downloadSpeed);

    // Sort berdasarkan response time (tercepat ke terlambat)
    const sortedByResponseTime = [...validProxies].sort((a, b) => a.connectTime - b.connectTime);

    console.log('\n🏆 RANKING PROXY TERCEPAT (Download Speed):');
    sortedBySpeed.forEach((proxy, index) => {
      const safeProxy = proxy.proxy.replace(/\/\/[^@]+@/, '//***:***@');
      const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
      console.log(`${medal} ${safeProxy}`);
      console.log(`    📍 IP: ${proxy.ip} | 🚀 ${proxy.downloadSpeed} KB/s | ⏱️ ${proxy.connectTime}ms`);
    });

    console.log('\n⚡ RANKING PROXY TERCEPAT (Response Time):');
    sortedByResponseTime.forEach((proxy, index) => {
      const safeProxy = proxy.proxy.replace(/\/\/[^@]+@/, '//***:***@');
      const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
      console.log(`${medal} ${safeProxy}`);
      console.log(`    📍 IP: ${proxy.ip} | ⏱️ ${proxy.connectTime}ms | 🚀 ${proxy.downloadSpeed} KB/s`);
    });

    // Tampilkan rekomendasi proxy terbaik
    const bestOverall = sortedBySpeed[0];
    const fastestResponse = sortedByResponseTime[0];

    console.log('\n🎯 REKOMENDASI:');
    console.log(`🏆 Proxy Terbaik Overall: ${bestOverall.proxy.replace(/\/\/[^@]+@/, '//***:***@')}`);
    console.log(`   📊 Download: ${bestOverall.downloadSpeed} KB/s | Response: ${bestOverall.connectTime}ms`);

    if (fastestResponse.proxy !== bestOverall.proxy) {
      console.log(`⚡ Proxy Tercepat Response: ${fastestResponse.proxy.replace(/\/\/[^@]+@/, '//***:***@')}`);
      console.log(`   📊 Response: ${fastestResponse.connectTime}ms | Download: ${fastestResponse.downloadSpeed} KB/s`);
    }
  }
})();
